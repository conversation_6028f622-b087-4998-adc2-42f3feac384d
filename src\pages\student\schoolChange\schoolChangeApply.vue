<route lang="json5">
{
  style: {
    navigationBarTitleText: '学期异动申请',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ActionButton from '@/components/common/ActionButton.vue'
import FileUploader from '@/components/common/FileUploader.vue'
import { getSemesterSelect } from '@/service/system'
import { getSchoolChangeApplyFiles } from '@/service/student'
import type { SemesterOption } from '@/types/semester'
import type { SchoolChangeApplyFileItem } from '@/types/student'

/** 路由实例 */
const router = useRouter()

/** 表单数据 */
const formData = ref({
  /** 异动申请学期 */
  semester: '',
  /** 异动类型 */
  changeType: '',
  /** 异动原因 */
  changeReason: '',
  /** 异动原因补充 */
  changeReasonDetails: '',
  /** 相关附件 */
  attachments: [] as Array<{ url: string; name: string }>,
})

/** 异动类型初始列表 */
const initList = [
  { value: '11', label: '休学' },
  { value: '12', label: '复学' },
  { value: '31', label: '退学' },
  { value: '19', label: '保留学籍' },
  { value: '07', label: '保留入学资格' },
]

/** 异动类型选项 */
const changeTypeOptions = ref(initList.map((item) => item.label))

/** 异动类型选择器索引 */
const changeTypeIndex = ref(-1)

/** 当前选择的异动类型值 */
const selectedChangeTypeValue = ref('')

/** 异动原因选项 */
const changeReasonOptions = ref<string[]>([])

/** 异动原因选择器索引 */
const changeReasonIndex = ref(-1)

/** 是否显示模板区域 */
const showTemplateSection = ref(false)

/** 证明材料模板文件列表 */
const templateFiles = ref<SchoolChangeApplyFileItem[]>([])

/** 加载模板文件列表 */
const loadTemplateFiles = async () => {
  try {
    const files = await getSchoolChangeApplyFiles()
    templateFiles.value = files
  } catch (error) {
    console.error('获取模板文件列表失败:', error)
    // 使用默认的模板文件列表作为降级方案
    templateFiles.value = [
      { name: '休学申请表.doc', url: '/templates/xuxue_shenqing.doc' },
      { name: '办理休学承诺书.docx', url: '/templates/xuxue_chengnuo.docx' },
      { name: '复学佐证材料(校医务室证明).doc', url: '/templates/fuxue_zhengming.doc' },
      { name: '退学申请表.doc', url: '/templates/tuixue_shenqing.doc' },
      { name: '休学申请表(二元制及退役军人).doc', url: '/templates/xuxue_eryuanzhi.doc' },
      { name: '退学申请表(二元制及退役军人).doc', url: '/templates/tuixue_eryuanzhi.doc' },
    ]
  }
}

/** 根据异动类型获取异动原因列表 */
const getTypeList = (code: string) => {
  let list: { value: string; label: string }[] = []
  switch (code) {
    case '07': // 保留入学资格
      list = [
        { value: '11', label: '精神疾病' },
        { value: '12', label: '传染疾病' },
        { value: '19', label: '其它疾病' },
        { value: '26', label: '家长病重' },
        { value: '96', label: '创新创业' },
        { value: '95', label: '社会实践' },
        { value: '94', label: '工作实践' },
        { value: '88', label: '参军' },
        { value: '99', label: '其它' },
      ]
      break
    case '11': // 休学
      list = [
        { value: '11', label: '精神疾病' },
        { value: '12', label: '传染疾病' },
        { value: '13', label: '心理疾病' },
        { value: '19', label: '其它疾病' },
        { value: '24', label: '休学创业' },
        { value: '26', label: '家长病重' },
        { value: '30', label: '自费留学' },
        { value: '27', label: '贫困' },
        { value: '96', label: '创新创业' },
        { value: '95', label: '社会实践' },
        { value: '94', label: '工作实践' },
        { value: '99', label: '其它原因' },
      ]
      break
    case '12': // 复学
      list = [
        { value: '89', label: '参军复员' },
        { value: '90', label: '休学期满' },
        { value: '99', label: '其它' },
      ]
      break
    case '19': // 保留学籍
      list = [{ value: '34', label: '应征入伍' }]
      break
    case '31': // 退学
      list = [
        { value: '22', label: '成绩低劣' },
        { value: '10', label: '疾病' },
        { value: '13', label: '心理疾病' },
        { value: '23', label: '触犯刑法' },
        { value: '21', label: '自动退学' },
        { value: '30', label: '自费留学' },
        { value: '32', label: '勒令退学' },
        { value: '17', label: '取消学籍' },
        { value: '27', label: '贫困' },
        { value: '24', label: '创业' },
        { value: '99', label: '其它' },
      ]
      break
  }
  return list
}

/** 获取学期数据 */
const fetchSemesterData = async () => {
  try {
    const res = await getSemesterSelect()
    // 设置默认学期
    const defaultSemester = res.data.find((item) => item.value === res.default)
    if (defaultSemester) {
      formData.value.semester = defaultSemester.label
    }
  } catch (error) {
    console.error('获取学期数据失败', error)
    // 设置默认值
    formData.value.semester = '2025-2026学年 第1学期'
  }
}

/** 异动类型选择器变化 */
const onChangeTypeChange = (e: any) => {
  const index = e.detail.value
  changeTypeIndex.value = index
  formData.value.changeType = changeTypeOptions.value[index]

  // 获取对应的异动类型值
  const selectedType = initList[index]
  if (selectedType) {
    selectedChangeTypeValue.value = selectedType.value
    // 根据异动类型获取对应的异动原因列表
    const reasonList = getTypeList(selectedType.value)
    changeReasonOptions.value = reasonList.map((item) => item.label)
    // 重置异动原因选择
    changeReasonIndex.value = -1
    formData.value.changeReason = ''
  }
}

/** 异动原因选择器变化 */
const onChangeReasonChange = (e: any) => {
  const index = e.detail.value
  changeReasonIndex.value = index
  formData.value.changeReason = changeReasonOptions.value[index]
}

/** 提交申请 */
const submitApplication = () => {
  // 表单验证
  if (!formData.value.changeType) {
    uni.showToast({
      title: '请选择异动类型',
      icon: 'none',
    })
    return
  }

  if (!formData.value.changeReason) {
    uni.showToast({
      title: '请选择异动原因',
      icon: 'none',
    })
    return
  }

  // TODO: 调用提交接口
  console.log('提交申请', formData.value)

  uni.showToast({
    title: '申请提交成功',
    icon: 'success',
  })

  // 返回列表页
  setTimeout(() => {
    router.back()
  }, 1500)
}

/** 返回 */
const goBack = () => {
  router.back()
}

/** 切换模板区域显示状态 */
const toggleTemplateSection = () => {
  showTemplateSection.value = !showTemplateSection.value
}

/** 获取文件图标 */
const getFileIcon = (fileName: string) => {
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
  switch (ext) {
    case 'doc':
    case 'docx':
      return 'file-word'
    case 'pdf':
      return 'file-pdf'
    case 'xls':
    case 'xlsx':
      return 'file-excel'
    case 'ppt':
    case 'pptx':
      return 'file-ppt'
    default:
      return 'file'
  }
}

/** 预览模板文件 */
const previewTemplate = (template: SchoolChangeApplyFileItem) => {
  // 根据文件类型选择预览方式
  const fileExt = template.name.split('.').pop()?.toLowerCase()

  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExt || '')) {
    // 图片文件直接预览
    uni.previewImage({
      urls: [template.url],
      current: template.url,
    })
  } else {
    // 非图片文件使用webview预览
    uni.navigateTo({
      url: `/pages/public/webview/webview?pdfreview=true&url=${encodeURIComponent(template.url)}&title=${encodeURIComponent(template.name)}`,
      fail: (err) => {
        console.error('打开文件失败', err)
        uni.showToast({
          title: '打开文件失败',
          icon: 'none',
        })
      },
    })
  }
}

/** 下载模板文件 */
const downloadTemplate = (template: SchoolChangeApplyFileItem) => {
  // 直接使用URL进行下载
  uni.downloadFile({
    url: template.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success',
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'error',
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '下载失败',
        icon: 'error',
      })
    },
  })
}

onMounted(() => {
  // 获取学期数据
  fetchSemesterData()
  // 加载模板文件列表
  loadTemplateFiles()
})
</script>

<template>
  <view class="apply-container bg-[#f2f2f7] min-h-screen">
    <!-- 表单内容 -->
    <view class="form-container p-[24rpx]">
      <!-- 表单卡片 -->
      <view class="form-card bg-white rounded-lg p-[24rpx] mb-[24rpx]">
        <!-- 异动申请学期 -->
        <view class="form-item mb-[20rpx]">
          <view class="form-label text-sm text-gray-600 mb-[12rpx]">异动申请学期</view>
          <view class="form-input p-[16rpx] bg-gray-50 rounded-lg">
            <text class="text-sm">{{ formData.semester || '加载中...' }}</text>
          </view>
        </view>

        <!-- 异动类型 -->
        <view class="form-item mb-[20rpx]">
          <view class="form-label text-sm text-gray-600 mb-[12rpx]">
            异动类型
            <text class="text-red-500">*</text>
          </view>
          <picker
            :value="changeTypeIndex"
            :range="changeTypeOptions"
            @change="onChangeTypeChange"
            class="form-picker"
          >
            <view
              class="form-input flex items-center justify-between p-[16rpx] bg-gray-50 rounded-lg"
            >
              <text class="text-sm" :class="{ 'text-gray-400': !formData.changeType }">
                {{ formData.changeType || '请选择异动类型' }}
              </text>
              <wd-icon name="arrow-right" custom-style="color: #999; font-size: 28rpx;" />
            </view>
          </picker>
        </view>

        <!-- 异动原因 -->
        <view class="form-item mb-[20rpx]">
          <view class="form-label text-sm text-gray-600 mb-[12rpx]">
            异动原因
            <text class="text-red-500">*</text>
          </view>
          <picker
            :value="changeReasonIndex"
            :range="changeReasonOptions"
            @change="onChangeReasonChange"
            :disabled="!formData.changeType"
            class="form-picker"
          >
            <view
              class="form-input flex items-center justify-between p-[16rpx] rounded-lg"
              :class="!formData.changeType ? 'bg-gray-200' : 'bg-gray-50'"
            >
              <text
                class="text-sm"
                :class="{ 'text-gray-400': !formData.changeReason || !formData.changeType }"
              >
                {{
                  !formData.changeType
                    ? '请先选择异动类型'
                    : formData.changeReason || '请选择异动原因'
                }}
              </text>
              <wd-icon name="arrow-right" custom-style="color: #999; font-size: 28rpx;" />
            </view>
          </picker>
        </view>

        <!-- 异动原因补充 -->
        <view class="form-item mb-[20rpx]">
          <view class="form-label text-sm text-gray-600 mb-[12rpx]">异动原因补充</view>
          <view class="textarea-container">
            <textarea
              v-model="formData.changeReasonDetails"
              class="form-textarea w-full p-[16rpx] bg-gray-50 rounded-lg text-sm box-border"
              placeholder="请详细说明异动原因..."
              :maxlength="500"
              :show-count="true"
              :auto-height="true"
            />
          </view>
        </view>

        <!-- 证明材料模板 -->
        <view class="form-item mb-[20rpx]">
          <view
            class="form-label text-sm text-gray-600 mb-[12rpx] flex items-center justify-between"
          >
            <text>证明材料模板</text>
            <view
              class="toggle-button flex items-center text-xs text-blue-500"
              @click="toggleTemplateSection"
            >
              <text>{{ showTemplateSection ? '收起' : '展开' }}</text>
              <wd-icon
                :name="showTemplateSection ? 'arrow-up' : 'arrow-down'"
                custom-style="color: #0083ff; font-size: 24rpx; margin-left: 8rpx;"
              />
            </view>
          </view>

          <!-- 可折叠的模板区域 -->
          <view
            class="template-section"
            :class="{ 'template-section-expanded': showTemplateSection }"
          >
            <view class="template-tip bg-blue-50 p-[16rpx] rounded-lg mb-[16rpx]">
              <text class="text-xs text-blue-600">
                学籍异动申请操作（休学、复学、退学异动类型需打印填写右侧表格）
              </text>
            </view>

            <view class="template-list">
              <view class="template-item" v-for="(template, index) in templateFiles" :key="index">
                <view class="template-info">
                  <wd-icon :name="getFileIcon(template.name)" size="18px" class="mr-2" />
                  <text class="template-name">{{ template.name }}</text>
                </view>
                <view class="template-actions">
                  <view class="template-action" @click="previewTemplate(template)">
                    <wd-icon name="view" size="16px" color="#1890ff" />
                    <text class="action-text">预览</text>
                  </view>
                  <view class="template-action" @click="downloadTemplate(template)">
                    <wd-icon name="download" size="16px" color="#52c41a" />
                    <text class="action-text">下载</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 相关附件 -->
        <view class="form-item">
          <FileUploader
            v-model="formData.attachments"
            title="相关附件"
            upload-type="schoolChange"
            tip-text="支持jpg、png、pdf等常见文件格式，最多上传5个文件"
          />
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view
      class="bottom-actions fixed bottom-0 left-0 right-0 bg-white p-[20rpx] border-t border-gray-100"
    >
      <view class="flex gap-[16rpx]">
        <ActionButton
          type="secondary"
          text="取消"
          @click="goBack"
          class="flex-1 py-[16rpx] text-center"
        />
        <ActionButton
          type="primary"
          text="提交申请"
          @click="submitApplication"
          class="flex-1 py-[16rpx] text-center"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.apply-container {
  padding-bottom: 100rpx; // 为底部按钮留出空间
}

.textarea-container {
  width: 100%;
  overflow: hidden; // 防止内容溢出
}

.form-textarea {
  box-sizing: border-box;
  width: 100% !important;
  max-width: 100%;
  min-height: 120rpx;
  word-break: break-all;
  word-wrap: break-word;
  resize: none;
  border: none;
  outline: none;
}

.form-input {
  min-height: 44rpx; // 减小最小高度
}

.form-item {
  .form-label {
    font-size: 28rpx; // 稍微减小标签字体
  }
}

.attachment-remove {
  opacity: 0.8;
  transition: opacity 0.2s;
}

.attachment-item {
  &:hover .attachment-remove {
    opacity: 1;
  }
}

.add-attachment {
  min-height: 120rpx; // 减小附件上传区域高度
  cursor: pointer;
  transition: border-color 0.2s;

  &:hover {
    border-color: #0083ff;
  }
}

.picker-item {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 模板区域样式
.toggle-button {
  cursor: pointer;
  transition: color 0.2s;

  &:hover {
    color: #0066cc;
  }
}

.template-section {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;

  &.template-section-expanded {
    max-height: 1000rpx; // 足够大的高度以容纳所有内容
  }
}

.template-tip {
  border-left: 4rpx solid #0083ff;
}

.template-list {
  width: 100%;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  margin-bottom: 12rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  transition: all 0.2s;

  &:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.template-info {
  display: flex;
  flex: 1;
  align-items: center;
  min-width: 0; // 允许文本截断
}

.template-name {
  overflow: hidden;
  font-size: 26rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-actions {
  display: flex;
  flex-shrink: 0;
  gap: 12rpx;
}

.template-action {
  display: flex;
  gap: 6rpx;
  align-items: center;
  padding: 8rpx 12rpx;
  cursor: pointer;
  background-color: #fff;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  transition: all 0.2s;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 2rpx 4rpx rgba(64, 169, 255, 0.2);
  }

  .action-text {
    font-size: 22rpx;
    color: #666;
  }

  &:first-child:hover .action-text {
    color: #1890ff;
  }

  &:last-child:hover .action-text {
    color: #52c41a;
  }
}
</style>
