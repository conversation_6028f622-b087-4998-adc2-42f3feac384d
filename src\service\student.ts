import request from '@/utils/request'
import { ClassmateInfo } from '@/types/student'
import type {
  StudentInfo,
  StudentInfoResponse,
  ScoreQueryParams,
  ScoreResponse,
  StudentTotalScoreQuery,
  StudentTotalScoreResponse,
  SaveStudentInfoParams,
  WeekResponse,
  SchoolChangeApplyQuery,
  SchoolChangeApplyResponse,
  SchoolChangeApplyFileItem,
  StudyPlanQuery,
  StudyPlanResponse,
  MoralEduQuery,
  MoralEduResponse,
  StudentFamilyQuery,
  StudentFamilyResponse,
  MoralEduRecordQuery,
  MoralEduRecordResponse,
  StudentScheduleSubmitParams,
  StudentScheduleSubmitResponse,
  ApplyLearningProveResponse,
  ApplyGraduateProveParams,
  ApplyGraduateProveResponse,
} from '@/types/student'

/**
 * 获取班级同学列表
 * @returns 班级同学信息列表
 */
export function getMyClassmates(): Promise<ClassmateInfo[]> {
  return request('/student/myClassmates', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取学生个人信息
 * @returns 学生个人信息
 */
export function getStudentInfo(): Promise<StudentInfo> {
  return request('/student/info', {
    method: 'GET',
  })
}

/**
 * 获取学生成绩列表
 * @param params 查询参数，包含分页信息、学期、班级名称、课程名称、课程性质和指导教师姓名等筛选条件
 * @returns 成绩列表响应，包含成绩项列表、统计信息和总记录数
 */
export function getStudentScore(params: ScoreQueryParams): Promise<ScoreResponse> {
  return request('/student/score', {
    method: 'POST',
    data: params,
  })
}

export function getStudentTotalScore(
  params: StudentTotalScoreQuery,
): Promise<StudentTotalScoreResponse> {
  return request('/student/myTotalScore', {
    method: 'POST',
    data: params,
  })
}

/**
 * 保存学生个人信息
 * @param params 要保存的学生信息
 * @returns 保存结果
 */
export function saveStudentInfo(params: SaveStudentInfoParams): Promise<string> {
  return request('/student/saveStudentInfo', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取当前周次和周次列表信息
 * @returns 周次信息，包含当前周次和周次列表
 */
export function getWeekInfo(): Promise<WeekResponse> {
  return request('/student/week', {
    method: 'GET',
  })
}

/**
 * 获取学籍异动申请列表
 * @param params 查询参数
 * @returns 学籍异动申请列表响应
 */
export function getSchoolChangeApply(
  params: SchoolChangeApplyQuery,
): Promise<SchoolChangeApplyResponse> {
  return request('/student/schoolChangeApply', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学籍异动申请文件列表
 * @returns 学籍异动申请文件列表
 */
export function getSchoolChangeApplyFiles(): Promise<SchoolChangeApplyFileItem[]> {
  return request('/student/schoolChangeApply/getSchoolChangeApplyFiles', {
    method: 'GET',
  })
}

/**
 * 获取学生学习计划列表
 * @param params 查询参数
 * @returns 学习计划列表响应
 */
export function getStudyPlan(params: StudyPlanQuery): Promise<StudyPlanResponse> {
  return request('/student/studyPlan', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生德育成绩列表
 * @param params 查询参数
 * @returns 德育成绩列表响应
 */
export function getMoralEdu(params: MoralEduQuery): Promise<MoralEduResponse> {
  return request('/student/moralEdu', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生家庭成员列表
 * @param params 查询参数
 * @returns 家庭成员列表响应
 */
export function getStudentFamily(params: StudentFamilyQuery): Promise<StudentFamilyResponse> {
  return request('/student/family', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生德育记录列表
 * @param params 查询参数
 * @returns 德育记录列表响应
 */
export function getMoralEduRecord(params: MoralEduRecordQuery): Promise<MoralEduRecordResponse> {
  return request('/student/moralEdu/record', {
    method: 'POST',
    data: params,
  })
}

/**
 * 提交学生课程表
 * @param params 提交参数，包含教学任务ID和记录ID
 * @returns 提交结果
 */
export function submitStudentSchedule(
  params: StudentScheduleSubmitParams,
): Promise<StudentScheduleSubmitResponse> {
  return request('/student/schedule/submit', {
    method: 'POST',
    data: params,
  })
}

/**
 * 申请打印在学证明
 * @returns 申请结果
 */
export function applyLearningProve(): Promise<ApplyLearningProveResponse['data']> {
  return request('/student/schoolChangeApply/applyLearningProve', {
    method: 'POST',
  })
}

/**
 * 申请打印预计可毕业证明
 * @param params 申请参数，包含操作类型
 * @returns 申请结果
 */
export function applyGraduateProve(
  params: ApplyGraduateProveParams,
): Promise<ApplyGraduateProveResponse['data']> {
  return request('/student/schoolChangeApply/applyGraduateProve', {
    method: 'POST',
    data: params,
  })
}
