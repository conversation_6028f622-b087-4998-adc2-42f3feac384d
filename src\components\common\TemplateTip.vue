<template>
  <view class="template-tip bg-blue-50 p-[16rpx] rounded-lg mb-[16rpx]" :class="tipClass">
    <text class="text-xs" :class="textClass">
      <slot>{{ text }}</slot>
    </text>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 提示类型定义
export type TipType = 'info' | 'warning' | 'success' | 'error' | 'default'

// Props定义
interface Props {
  /** 提示类型 */
  type?: TipType
  /** 提示文本 */
  text?: string
  /** 是否显示左侧边框 */
  showBorder?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  text: '',
  showBorder: true,
})

// 计算提示框样式类
const tipClass = computed(() => {
  const typeClasses: Record<TipType, string> = {
    info: 'bg-blue-50',
    warning: 'bg-orange-50',
    success: 'bg-green-50',
    error: 'bg-red-50',
    default: 'bg-gray-50',
  }

  const borderClasses: Record<TipType, string> = {
    info: 'border-l-blue-500',
    warning: 'border-l-orange-500',
    success: 'border-l-green-500',
    error: 'border-l-red-500',
    default: 'border-l-gray-500',
  }

  let classes = typeClasses[props.type]

  if (props.showBorder) {
    classes += ` border-l-8rpx border-l-solid ${borderClasses[props.type]}`
  }

  return classes
})

// 计算文本样式类
const textClass = computed(() => {
  const typeClasses: Record<TipType, string> = {
    info: 'text-blue-600',
    warning: 'text-orange-600',
    success: 'text-green-600',
    error: 'text-red-600',
    default: 'text-gray-600',
  }

  return typeClasses[props.type]
})
</script>

<style lang="scss" scoped>
// 组件样式已通过UnoCSS类实现，无需额外样式
</style>
